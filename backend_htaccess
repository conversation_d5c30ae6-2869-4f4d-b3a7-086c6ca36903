<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>

    RewriteEngine On

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]

    # Send Requests To Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>

# Security Headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Strict-Transport-Security "max-age=63072000; includeSubDomains; preload"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# CORS Headers for API
<IfModule mod_headers.c>
    Header add Access-Control-Allow-Origin "https://lightcyan-goldfish-196353.hostingersite.com"
    Header add Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header add Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With, Accept"
    Header add Access-Control-Allow-Credentials "true"
</IfModule>

# Handle preflight OPTIONS requests
<IfModule mod_rewrite.c>
    RewriteCond %{REQUEST_METHOD} OPTIONS
    RewriteRule ^(.*)$ $1 [R=200,L]
</IfModule>

# Hide sensitive files
<Files ".env">
    Order allow,deny
    Deny from all
</Files>

<Files ".env.*">
    Order allow,deny
    Deny from all
</Files>

<Files "composer.json">
    Order allow,deny
    Deny from all
</Files>

<Files "composer.lock">
    Order allow,deny
    Deny from all
</Files>

<Files "artisan">
    Order allow,deny
    Deny from all
</Files>

<Files "*.md">
    Order allow,deny
    Deny from all
</Files>

<Files "*.log">
    Order allow,deny
    Deny from all
</Files>

# Disable directory browsing
Options -Indexes

# Prevent access to storage and bootstrap cache
<IfModule mod_rewrite.c>
    RewriteCond %{REQUEST_URI} ^/storage/.*
    RewriteRule ^(.*)$ - [F,L]
    
    RewriteCond %{REQUEST_URI} ^/bootstrap/cache/.*
    RewriteRule ^(.*)$ - [F,L]
</IfModule>

# PHP Security
<IfModule mod_php7.c>
    php_flag display_errors Off
    php_flag log_errors On
    php_flag expose_php Off
</IfModule>

<IfModule mod_php8.c>
    php_flag display_errors Off
    php_flag log_errors On
    php_flag expose_php Off
</IfModule>
