#!/usr/bin/env php
<?php

define('LARAVEL_START', microtime(true));

if (file_exists($file = __DIR__.'/vendor/autoload.php')) {
    require_once $file;
} else {
    echo 'Please run "composer install" to install the application\'s dependencies.'.PHP_EOL;
    exit(1);
}

$app = require_once __DIR__.'/bootstrap/app.php';

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);

$status = $kernel->handle(
    $input = new Symfony\Component\Console\Input\ArgvInput,
    new Symfony\Component\Console\Output\ConsoleOutput
);

$kernel->terminate($input, $status);

exit($status);