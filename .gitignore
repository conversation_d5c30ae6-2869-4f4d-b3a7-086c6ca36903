# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
*.tmp
*.temp

# IDE files
.vscode/*
!.vscode/extensions.json
.idea/
*.swp
*.swo
*~

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.backup

# Dependencies
node_modules/
vendor/

# Build outputs
dist/
build/
dist-ssr/
*.tgz
*.tar.gz

# Cache directories
.cache/
.parcel-cache/
.next/
.nuxt/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory
coverage/
*.lcov
.nyc_output/

# Laravel specific (backend)
backend/vendor/
backend/node_modules/
backend/public/hot
backend/public/storage
backend/storage/*.key
backend/.env
backend/.env.backup
backend/.env.production
backend/.phpunit.result.cache
backend/docker-compose.override.yml
backend/Homestead.json
backend/Homestead.yaml
backend/npm-debug.log
backend/yarn-error.log
backend/bootstrap/cache/*.php
backend/storage/framework/cache/*
backend/storage/framework/sessions/*
backend/storage/framework/views/*
backend/storage/logs/*.log

# React/Vite specific (frontend)
frontend/node_modules/
frontend/dist/
frontend/.env
frontend/.env.local
frontend/.env.development.local
frontend/.env.test.local
frontend/.env.production.local
frontend/npm-debug.log*
frontend/yarn-debug.log*
frontend/yarn-error.log*

# Database files
*.sqlite
*.sqlite3
*.db

# Backup files
*.bak
*.backup
*.old

# Compiled files
*.com
*.class
*.dll
*.exe
*.o
*.so

# Packages
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Test files
/tests/
/test/
*.test.js
*.spec.js

# Local configuration
config.local.*
*.local

# Temporary folders
tmp/
temp/

# TypeScript cache
*.tsbuildinfo

# Optional caches
.npm
.eslintcache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Yarn
.yarn-integrity
yarn-error.log

# Expo
.expo/
.expo-shared/

# Netlify
.netlify/

# Stores VSCode versions
.vscode-test/
