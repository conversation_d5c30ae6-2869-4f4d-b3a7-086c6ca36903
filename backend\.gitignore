# Laravel specific
/vendor/
/node_modules/
/public/hot
/public/storage
/storage/*.key
.env
.env.backup
.env.production
.phpunit.result.cache
docker-compose.override.yml
Homestead.json
Homestead.yaml
npm-debug.log
yarn-error.log
/.idea
/.vscode

# Database
*.sqlite
*.sqlite3
database.sqlite

# Logs
/storage/logs/*.log

# Cache
/bootstrap/cache/*.php
/storage/framework/cache/*
/storage/framework/sessions/*
/storage/framework/views/*

# Compiled assets
/public/mix-manifest.json
/public/js/
/public/css/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Backup files
*.bak
*.backup
*.old

# Temporary files
*.tmp
*.temp
