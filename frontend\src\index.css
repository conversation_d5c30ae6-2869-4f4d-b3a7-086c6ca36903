@tailwind base;
@tailwind components;
@tailwind utilities;

/* Dark Mode Overrides - <PERSON><PERSON> */
.dark {
  color-scheme: dark;
}

.dark body {
  background-color: #0a0a0a !important;
  color: #e5e5e5 !important;
}

/* Card backgrounds - <PERSON><PERSON> pekat */
.dark .bg-white {
  background-color: #111111 !important;
  border-color: #1f1f1f !important;
}

.dark .bg-gray-50 {
  background-color: #0a0a0a !important;
}

.dark .bg-gray-100 {
  background-color: #111111 !important;
}

.dark .bg-gray-200 {
  background-color: #1a1a1a !important;
}

/* Text colors - Kontras tinggi */
.dark .text-gray-900 {
  color: #f5f5f5 !important;
}

.dark .text-gray-800 {
  color: #e5e5e5 !important;
}

.dark .text-gray-700 {
  color: #d4d4d4 !important;
}

.dark .text-gray-600 {
  color: #a3a3a3 !important;
}

.dark .text-gray-500 {
  color: #737373 !important;
}

/* Border colors */
.dark .border-gray-200 {
  border-color: #1f1f1f !important;
}

.dark .border-gray-300 {
  border-color: #2a2a2a !important;
}

/* Input fields */
.dark input,
.dark textarea,
.dark select {
  background-color: #1a1a1a !important;
  border-color: #2a2a2a !important;
  color: #e5e5e5 !important;
}

.dark input:focus,
.dark textarea:focus,
.dark select:focus {
  background-color: #1f1f1f !important;
  border-color: #3b82f6 !important;
}

/* Dropdown menus */
.dark .bg-white.shadow-lg {
  background-color: #111111 !important;
  border-color: #1f1f1f !important;
}

/* Charts background */
.dark .recharts-wrapper {
  background-color: transparent !important;
}

.dark .recharts-cartesian-grid line {
  stroke: #404040 !important; /* Lighter grid lines for better visibility */
  stroke-width: 0.5 !important;
}

/* Table borders for better visibility */
.dark table, .dark th, .dark td {
  border-color: #404040 !important;
}

.dark .border {
  border-color: #404040 !important;
}

.dark .border-gray-200 {
  border-color: #404040 !important;
}

.dark .border-gray-300 {
  border-color: #505050 !important;
}

/* Chart tooltips */
.dark .recharts-tooltip-wrapper {
  background-color: #111111 !important;
  border: 1px solid #2a2a2a !important;
  border-radius: 0.5rem !important;
  color: #e5e5e5 !important;
}

.dark .recharts-default-tooltip {
  background-color: #111111 !important;
  border: 1px solid #2a2a2a !important;
  color: #e5e5e5 !important;
}

/* Chart axis text */
.dark .recharts-text {
  fill: #a3a3a3 !important;
}

/* CSS Variables for dynamic theming */
:root {
  --tooltip-bg: white;
  --tooltip-border: #e5e7eb;
  --tooltip-text: #374151;
  --chart-grid-color: #e0e0e0;
  --chart-text-color: #6b7280;
  --toast-bg: white;
  --toast-border: #e5e7eb;
  --toast-text: #374151;
}

.dark {
  --tooltip-bg: #111111;
  --tooltip-border: #2a2a2a;
  --tooltip-text: #e5e5e5;
  --chart-grid-color: #2a2a2a;
  --chart-text-color: #a3a3a3;
  --toast-bg: #111111;
  --toast-border: #2a2a2a;
  --toast-text: #e5e5e5;
}

/* Scrollbar styling for dark mode */
.dark ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.dark ::-webkit-scrollbar-track {
  background: #0a0a0a;
}

.dark ::-webkit-scrollbar-thumb {
  background: #2a2a2a;
  border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #3a3a3a;
}

/* Force dark background for any remaining white elements */
.dark * {
  border-color: #2a2a2a;
}

.dark .shadow-sm {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3) !important;
}

.dark .shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -2px rgba(0, 0, 0, 0.3) !important;
}

.dark .shadow-xl {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.6), 0 10px 10px -5px rgba(0, 0, 0, 0.4) !important;
}

/* Toast styling for dark mode */
.dark .react-hot-toast-container {
  color: #e5e5e5 !important;
}

.dark .react-hot-toast {
  background-color: #111111 !important;
  color: #e5e5e5 !important;
  border: 1px solid #2a2a2a !important;
}

.dark .react-hot-toast .react-hot-toast-icon {
  color: #e5e5e5 !important;
}

.dark .react-hot-toast-success {
  background-color: #111111 !important;
  color: #e5e5e5 !important;
}

.dark .react-hot-toast-error {
  background-color: #111111 !important;
  color: #e5e5e5 !important;
}

/* Ensure all backgrounds are dark */
.dark div:not([class*="bg-"]):not([style*="background"]) {
  background-color: transparent;
}

/* Modal and dropdown backgrounds */
.dark .fixed.inset-0 {
  background-color: rgba(0, 0, 0, 0.8) !important;
}

/* Button hover states */
.dark .hover\:bg-gray-50:hover {
  background-color: #1a1a1a !important;
}

.dark .hover\:bg-gray-100:hover {
  background-color: #1f1f1f !important;
}

/* Table styles */
.dark table {
  background-color: #111111 !important;
}

.dark th {
  background-color: #1a1a1a !important;
  color: #e5e5e5 !important;
}

.dark td {
  background-color: #111111 !important;
  color: #d4d4d4 !important;
  border-color: #2a2a2a !important;
}
